"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/success/page",{

/***/ "(app-pages-browser)/./src/hooks/useSubscription.ts":
/*!**************************************!*\
  !*** ./src/hooks/useSubscription.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSubscription: () => (/* binding */ useSubscription)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n\n\nfunction useSubscription() {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [subscriptionStatus, setSubscriptionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [usageStatus, setUsageStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_1__.createSupabaseBrowserClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSubscription.useEffect\": ()=>{\n            // Get initial user (more secure than getSession)\n            const getUser = {\n                \"useSubscription.useEffect.getUser\": async ()=>{\n                    const { data: { user } } = await supabase.auth.getUser();\n                    setUser(user !== null && user !== void 0 ? user : null);\n                    if (user) {\n                        fetchSubscriptionStatus(user);\n                        fetchUsageStatus(user);\n                    } else {\n                        setLoading(false);\n                    }\n                }\n            }[\"useSubscription.useEffect.getUser\"];\n            getUser();\n            // Listen for auth changes\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"useSubscription.useEffect\": async (event, session)=>{\n                    console.log('Auth state change in useSubscription:', event, !!session);\n                    var _session_user;\n                    setUser((_session_user = session === null || session === void 0 ? void 0 : session.user) !== null && _session_user !== void 0 ? _session_user : null);\n                    if (session === null || session === void 0 ? void 0 : session.user) {\n                        // Clear any cached data when switching users\n                        setSubscriptionStatus(null);\n                        setUsageStatus(null);\n                        setLoading(true);\n                        // Clear user-specific cache entries\n                        try {\n                            const { clearUserSpecificCache } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_utils_clearUserCache_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/utils/clearUserCache */ \"(app-pages-browser)/./src/utils/clearUserCache.ts\"));\n                            await clearUserSpecificCache(session.user.id);\n                            console.log('User-specific cache cleared for new user:', session.user.id);\n                        } catch (cacheError) {\n                            console.warn('Could not clear user cache:', cacheError);\n                        }\n                        // Small delay to ensure cache clearing is complete\n                        await new Promise({\n                            \"useSubscription.useEffect\": (resolve)=>setTimeout(resolve, 100)\n                        }[\"useSubscription.useEffect\"]);\n                        // Fetch fresh data for the new user\n                        await Promise.all([\n                            fetchSubscriptionStatus(session.user),\n                            fetchUsageStatus(session.user)\n                        ]);\n                    } else {\n                        // User signed out - clear all data\n                        setSubscriptionStatus(null);\n                        setUsageStatus(null);\n                        setError(null);\n                        setLoading(false);\n                    }\n                }\n            }[\"useSubscription.useEffect\"]);\n            return ({\n                \"useSubscription.useEffect\": ()=>subscription.unsubscribe()\n            })[\"useSubscription.useEffect\"];\n        }\n    }[\"useSubscription.useEffect\"], []);\n    const fetchSubscriptionStatus = async (currentUser)=>{\n        try {\n            console.log('Fetching subscription status for user:', currentUser.id);\n            // Force fresh data by adding cache-busting timestamp\n            const timestamp = Date.now();\n            const response = await fetch(\"/api/stripe/subscription-status?userId=\".concat(currentUser.id, \"&_t=\").concat(timestamp), {\n                cache: 'no-store',\n                headers: {\n                    'Cache-Control': 'no-cache, no-store, must-revalidate',\n                    'Pragma': 'no-cache',\n                    'Expires': '0'\n                }\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Subscription status API error:', {\n                    status: response.status,\n                    statusText: response.statusText,\n                    errorText\n                });\n                throw new Error(\"Failed to fetch subscription status: \".concat(response.status, \" \").concat(response.statusText));\n            }\n            const data = await response.json();\n            console.log('Subscription status data:', data);\n            setSubscriptionStatus(data);\n        } catch (err) {\n            console.error('Error fetching subscription status:', err);\n            // Don't set error state for network issues during portal returns\n            // This prevents the \"Failed to fetch\" errors from showing to users\n            if (err instanceof Error && err.message.includes('Failed to fetch')) {\n                console.log('Network error during subscription fetch, will retry...');\n                // Set a fallback state instead of error\n                setSubscriptionStatus({\n                    hasActiveSubscription: false,\n                    tier: 'free',\n                    status: null,\n                    currentPeriodEnd: null,\n                    cancelAtPeriodEnd: false,\n                    isFree: true\n                });\n            } else {\n                setError(err instanceof Error ? err.message : 'Unknown error');\n            }\n        }\n    };\n    const fetchUsageStatus = async (currentUser)=>{\n        try {\n            // Force fresh data with cache-busting headers\n            const response = await fetch('/api/stripe/subscription-status', {\n                method: 'POST',\n                cache: 'no-store',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Cache-Control': 'no-cache, no-store, must-revalidate',\n                    'Pragma': 'no-cache',\n                    'Expires': '0'\n                },\n                body: JSON.stringify({\n                    userId: currentUser.id,\n                    _t: Date.now() // Cache buster\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to fetch usage status');\n            }\n            const data = await response.json();\n            console.log('Usage status data:', data);\n            setUsageStatus(data);\n        } catch (err) {\n            console.error('Error fetching usage status:', err);\n            // Don't set error state for network issues during portal returns\n            if (err instanceof Error && err.message.includes('Failed to fetch')) {\n                console.log('Network error during usage fetch, will retry...');\n                // Set a fallback state instead of error\n                setUsageStatus({\n                    tier: 'free',\n                    usage: {\n                        configurations: 0,\n                        apiKeys: 0,\n                        apiRequests: 0\n                    },\n                    limits: {\n                        configurations: 1,\n                        apiKeysPerConfig: 3,\n                        apiRequests: 999999,\n                        canUseAdvancedRouting: false,\n                        canUseCustomRoles: false,\n                        maxCustomRoles: 0,\n                        canUsePromptEngineering: false,\n                        canUseKnowledgeBase: false,\n                        knowledgeBaseDocuments: 0,\n                        canUseSemanticCaching: false\n                    },\n                    canCreateConfig: true,\n                    canCreateApiKey: true\n                });\n            } else {\n                setError(err instanceof Error ? err.message : 'Unknown error');\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createCheckoutSession = async (tier)=>{\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const response = await fetch('/api/stripe/create-checkout-session', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                priceId: getPriceIdForTier(tier),\n                userId: user.id,\n                userEmail: user.email,\n                tier\n            })\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.error || 'Failed to create checkout session');\n        }\n        const { url } = await response.json();\n        window.location.href = url;\n    };\n    const openCustomerPortal = async (returnUrl)=>{\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const response = await fetch('/api/stripe/customer-portal', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                userId: user.id,\n                returnUrl\n            })\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.error || 'Failed to open customer portal');\n        }\n        const { url } = await response.json();\n        window.location.href = url;\n    };\n    const refreshStatus = async ()=>{\n        if (user) {\n            console.log('Manual refresh triggered for user:', user.id);\n            setLoading(true);\n            setError(null); // Clear any previous errors\n            // Clear cache before refreshing\n            try {\n                const { clearUserSpecificCache } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_utils_clearUserCache_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/utils/clearUserCache */ \"(app-pages-browser)/./src/utils/clearUserCache.ts\"));\n                await clearUserSpecificCache(user.id);\n                console.log('Cache cleared before manual refresh');\n            } catch (error) {\n                console.warn('Failed to clear cache before refresh:', error);\n            }\n            // Clear browser cache for subscription endpoints\n            try {\n                if ('caches' in window) {\n                    const cacheNames = await caches.keys();\n                    await Promise.all(cacheNames.map((cacheName)=>caches.delete(cacheName)));\n                    console.log('Browser caches cleared');\n                }\n            } catch (error) {\n                console.warn('Failed to clear browser caches:', error);\n            }\n            // Longer delay to ensure cache clearing is complete and webhook processing\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n            try {\n                // Force fresh fetch by clearing current state first\n                setSubscriptionStatus(null);\n                setUsageStatus(null);\n                // Fetch fresh data\n                await Promise.all([\n                    fetchSubscriptionStatus(user),\n                    fetchUsageStatus(user)\n                ]);\n                console.log('Manual refresh completed successfully');\n            } catch (error) {\n                console.error('Error during manual refresh:', error);\n                setError(error instanceof Error ? error.message : 'Failed to refresh subscription status');\n            }\n        }\n    };\n    return {\n        subscriptionStatus,\n        usageStatus,\n        loading,\n        error,\n        createCheckoutSession,\n        openCustomerPortal,\n        refreshStatus,\n        isAuthenticated: !!user,\n        user\n    };\n}\nfunction getPriceIdForTier(tier) {\n    switch(tier){\n        case 'free':\n            return \"price_1RcfRzC97XFBBUvd9XqkXe3a\";\n        case 'starter':\n            return \"price_1RcfWSC97XFBBUvdYHXbBvQ6\";\n        case 'professional':\n            return \"price_1RcfViC97XFBBUvdxwixKUdg\";\n        case 'enterprise':\n            return \"price_1RaADDC97XFBBUvd7j6OPJj7\";\n        default:\n            throw new Error(\"Invalid tier: \".concat(tier));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSubscription.ts\n"));

/***/ })

});