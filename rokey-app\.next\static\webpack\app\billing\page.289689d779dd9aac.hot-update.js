"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/billing/page",{

/***/ "(app-pages-browser)/./src/components/DebugSubscriptionStatus.tsx":
/*!****************************************************!*\
  !*** ./src/components/DebugSubscriptionStatus.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DebugSubscriptionStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction DebugSubscriptionStatus() {\n    _s();\n    const { user } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_2__.useSubscription)();\n    const [debugData, setDebugData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fetchDebugData = async ()=>{\n        if (!user) {\n            setError('No user logged in');\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        try {\n            const response = await fetch(\"/api/debug/subscription-data?userId=\".concat(user.id, \"&_t=\").concat(Date.now()), {\n                cache: 'no-store',\n                headers: {\n                    'Cache-Control': 'no-cache, no-store, must-revalidate',\n                    'Pragma': 'no-cache',\n                    'Expires': '0'\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const data = await response.json();\n            setDebugData(data);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Unknown error');\n            console.error('Debug fetch error:', err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-yellow-800\",\n                children: \"Please log in to view subscription debug data.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                lineNumber: 79,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 bg-white border border-gray-200 rounded-lg shadow-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"\\uD83D\\uDD0D Subscription Debug Tool\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchDebugData,\n                        disabled: loading,\n                        className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                        children: loading ? 'Loading...' : 'Fetch Debug Data'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-800 text-sm\",\n                    children: [\n                        \"Error: \",\n                        error\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                lineNumber: 98,\n                columnNumber: 9\n            }, this),\n            debugData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-900 mb-2\",\n                                        children: \"\\uD83D\\uDC64 User Profile\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Tier:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 bg-blue-100 text-blue-800 rounded\",\n                                                        children: debugData.profile.subscription_tier\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 63\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Status:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 bg-green-100 text-green-800 rounded\",\n                                                        children: debugData.profile.subscription_status\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"User Status:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 bg-purple-100 text-purple-800 rounded\",\n                                                        children: debugData.profile.user_status\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 70\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Updated:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" \",\n                                                    new Date(debugData.profile.updated_at).toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-900 mb-2\",\n                                        children: \"\\uD83C\\uDFAF Active Subscription\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, this),\n                                    debugData.active_subscription.data ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Tier:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 22\n                                                    }, this),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 bg-green-100 text-green-800 rounded\",\n                                                        children: debugData.active_subscription.data.tier\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Status:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 22\n                                                    }, this),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 bg-green-100 text-green-800 rounded\",\n                                                        children: debugData.active_subscription.data.status\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 67\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Updated:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 22\n                                                    }, this),\n                                                    \" \",\n                                                    new Date(debugData.active_subscription.data.updated_at).toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Stripe ID:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 22\n                                                    }, this),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-mono bg-gray-200 px-1 rounded\",\n                                                        children: debugData.active_subscription.data.stripe_subscription_id\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 70\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"No active subscription found\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 17\n                                    }, this),\n                                    debugData.active_subscription.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-red-600 mt-2\",\n                                        children: [\n                                            \"Error: \",\n                                            debugData.active_subscription.error\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-blue-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-semibold text-gray-900 mb-2\",\n                                children: \"\\uD83E\\uDDE0 Tier Determination Logic\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-700 mb-2\",\n                                children: debugData.tier_determination.final_tier_logic\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-5 gap-2 text-xs\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Profile:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-1 py-0.5 bg-gray-200 rounded\",\n                                                children: debugData.tier_determination.profile_tier\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Latest Sub:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-1 py-0.5 bg-gray-200 rounded\",\n                                                children: debugData.tier_determination.latest_subscription_tier || 'N/A'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Latest Status:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-1 py-0.5 bg-gray-200 rounded\",\n                                                children: debugData.tier_determination.latest_subscription_status || 'N/A'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Active Sub:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-1 py-0.5 bg-gray-200 rounded\",\n                                                children: debugData.tier_determination.active_subscription_tier || 'N/A'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Active Status:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-1 py-0.5 bg-gray-200 rounded\",\n                                                children: debugData.tier_determination.active_subscription_status || 'N/A'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-gray-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-semibold text-gray-900 mb-2\",\n                                children: [\n                                    \"\\uD83D\\uDCCB All Subscriptions (\",\n                                    debugData.subscriptions.total_count,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this),\n                            debugData.subscriptions.all_subscriptions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: debugData.subscriptions.all_subscriptions.map((sub, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-white border rounded text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    \"#\",\n                                                                    index + 1\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                                lineNumber: 178,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \" -\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 bg-blue-100 text-blue-800 rounded ml-1\",\n                                                                children: sub.tier\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                                lineNumber: 179,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 rounded ml-1 \".concat(sub.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                                children: sub.status\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            \"Updated: \",\n                                                            new Date(sub.updated_at).toLocaleString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-600 mt-1 font-mono\",\n                                                children: sub.stripe_subscription_id\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, sub.id, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"No subscriptions found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-500 text-center\",\n                        children: [\n                            \"Last fetched: \",\n                            new Date(debugData.timestamp).toLocaleString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                lineNumber: 104,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\n_s(DebugSubscriptionStatus, \"KBgfzzJCW/TozjP5vHrs2isRNrA=\", false, function() {\n    return [\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_2__.useSubscription\n    ];\n});\n_c = DebugSubscriptionStatus;\nvar _c;\n$RefreshReg$(_c, \"DebugSubscriptionStatus\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DebugSubscriptionStatus.tsx\n"));

/***/ })

});