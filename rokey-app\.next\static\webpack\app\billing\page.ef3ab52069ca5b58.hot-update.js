"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/billing/page",{

/***/ "(app-pages-browser)/./src/app/billing/page.tsx":
/*!**********************************!*\
  !*** ./src/app/billing/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BillingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CreditCardIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var _components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/ConfirmationModal */ \"(app-pages-browser)/./src/components/ui/ConfirmationModal.tsx\");\n/* harmony import */ var canvas_confetti__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! canvas-confetti */ \"(app-pages-browser)/./node_modules/canvas-confetti/dist/confetti.module.mjs\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* harmony import */ var _emailjs_browser__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @emailjs/browser */ \"(app-pages-browser)/./node_modules/@emailjs/browser/es/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst plans = [\n    {\n        id: 'free',\n        name: 'Free',\n        price: 0,\n        interval: 'forever',\n        features: [\n            {\n                name: 'Strict fallback routing only',\n                included: true\n            },\n            {\n                name: 'Basic analytics',\n                included: true\n            },\n            {\n                name: 'No custom roles',\n                included: false\n            },\n            {\n                name: 'Configurations',\n                included: true,\n                limit: '1 max'\n            },\n            {\n                name: 'API keys per config',\n                included: true,\n                limit: '3 max'\n            },\n            {\n                name: 'User-generated API keys',\n                included: true,\n                limit: '3 max'\n            }\n        ]\n    },\n    {\n        id: 'starter',\n        name: 'Starter',\n        price: 19,\n        interval: 'month',\n        popular: true,\n        features: [\n            {\n                name: 'All routing strategies',\n                included: true\n            },\n            {\n                name: 'Advanced analytics',\n                included: true\n            },\n            {\n                name: 'Custom roles',\n                included: true,\n                limit: 'Up to 3 roles'\n            },\n            {\n                name: 'Configurations',\n                included: true,\n                limit: '5 max'\n            },\n            {\n                name: 'API keys per config',\n                included: true,\n                limit: '15 max'\n            },\n            {\n                name: 'User-generated API keys',\n                included: true,\n                limit: '50 max'\n            },\n            {\n                name: 'Browsing tasks',\n                included: true,\n                limit: '15/month'\n            }\n        ]\n    },\n    {\n        id: 'professional',\n        name: 'Professional',\n        price: 49,\n        interval: 'month',\n        features: [\n            {\n                name: 'Everything in Starter',\n                included: true\n            },\n            {\n                name: 'Unlimited configurations',\n                included: true\n            },\n            {\n                name: 'Unlimited API keys per config',\n                included: true\n            },\n            {\n                name: 'Unlimited user-generated API keys',\n                included: true\n            },\n            {\n                name: 'Knowledge base documents',\n                included: true,\n                limit: '5 documents'\n            },\n            {\n                name: 'Priority support',\n                included: true\n            }\n        ]\n    },\n    {\n        id: 'enterprise',\n        name: 'Enterprise',\n        price: 149,\n        interval: 'month',\n        features: [\n            {\n                name: 'Everything in Professional',\n                included: true\n            },\n            {\n                name: 'Unlimited knowledge base documents',\n                included: true\n            },\n            {\n                name: 'Advanced semantic caching',\n                included: true\n            },\n            {\n                name: 'Custom integrations',\n                included: true\n            },\n            {\n                name: 'Dedicated support + phone',\n                included: true\n            },\n            {\n                name: 'SLA guarantee',\n                included: true\n            }\n        ]\n    }\n];\nfunction BillingPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { user, subscriptionStatus, refreshStatus: refreshSubscription, createCheckoutSession, openCustomerPortal } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_6__.useSubscription)();\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_9__.useConfirmation)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCancelModal, setShowCancelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cancelReason, setCancelReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [cancelFeedback, setCancelFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tierBeforePortal, setTierBeforePortal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const currentPlan = plans.find((plan)=>plan.id === (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier)) || plans[0];\n    // Calculate days until renewal based on actual subscription data\n    const daysUntilRenewal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"BillingPage.useMemo[daysUntilRenewal]\": ()=>{\n            if ((subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' || !(subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.currentPeriodEnd)) {\n                return null;\n            }\n            const renewalDate = new Date(subscriptionStatus.currentPeriodEnd);\n            const today = new Date();\n            const diffTime = renewalDate.getTime() - today.getTime();\n            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n            // Return null if subscription has already expired\n            return diffDays > 0 ? diffDays : null;\n        }\n    }[\"BillingPage.useMemo[daysUntilRenewal]\"], [\n        subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier,\n        subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.currentPeriodEnd\n    ]);\n    // Helper function to determine if a tier change is an upgrade\n    const isUpgrade = (fromTier, toTier)=>{\n        const tierOrder = {\n            'free': 0,\n            'starter': 1,\n            'professional': 2,\n            'enterprise': 3\n        };\n        return tierOrder[toTier] > tierOrder[fromTier];\n    };\n    // Handle return from Customer Portal\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BillingPage.useEffect\": ()=>{\n            const portalReturn = searchParams.get('portal_return');\n            const previousTier = searchParams.get('prev_tier');\n            if (portalReturn === 'true') {\n                console.log('Returned from Customer Portal, previous tier:', previousTier);\n                // Remove the parameters from URL\n                const newUrl = new URL(window.location.href);\n                newUrl.searchParams.delete('portal_return');\n                newUrl.searchParams.delete('prev_tier');\n                window.history.replaceState({}, '', newUrl.toString());\n                // Wait for webhook to process, then refresh subscription status\n                const refreshWithRetry = {\n                    \"BillingPage.useEffect.refreshWithRetry\": async ()=>{\n                        let attempts = 0;\n                        const maxAttempts = 8; // Increased attempts\n                        const initialDelay = 3000; // Longer initial delay for webhook processing\n                        const retryDelay = 2000; // 2 seconds between retry attempts\n                        console.log('Starting subscription refresh with retry logic...');\n                        // Initial delay to allow webhook processing\n                        await new Promise({\n                            \"BillingPage.useEffect.refreshWithRetry\": (resolve)=>setTimeout(resolve, initialDelay)\n                        }[\"BillingPage.useEffect.refreshWithRetry\"]);\n                        while(attempts < maxAttempts){\n                            try {\n                                console.log(\"Refresh attempt \".concat(attempts + 1, \"/\").concat(maxAttempts));\n                                // Force a complete refresh\n                                await refreshSubscription();\n                                // Wait a moment for the state to update\n                                await new Promise({\n                                    \"BillingPage.useEffect.refreshWithRetry\": (resolve)=>setTimeout(resolve, 500)\n                                }[\"BillingPage.useEffect.refreshWithRetry\"]);\n                                // Get fresh subscription status after refresh\n                                const response = await fetch(\"/api/stripe/subscription-status?userId=\".concat(user === null || user === void 0 ? void 0 : user.id, \"&_t=\").concat(Date.now()), {\n                                    cache: 'no-store',\n                                    headers: {\n                                        'Cache-Control': 'no-cache, no-store, must-revalidate',\n                                        'Pragma': 'no-cache',\n                                        'Expires': '0'\n                                    }\n                                });\n                                if (response.ok) {\n                                    const freshStatus = await response.json();\n                                    const currentTier = freshStatus.tier || 'free';\n                                    console.log('Fresh tier from API:', currentTier, 'Previous tier:', previousTier);\n                                    // Check if tier actually changed\n                                    if (currentTier !== previousTier) {\n                                        const wasUpgrade = isUpgrade(previousTier || 'free', currentTier);\n                                        if (wasUpgrade) {\n                                            // Show upgrade success message with confetti\n                                            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success('Plan upgraded successfully!');\n                                            // Trigger confetti for upgrades only\n                                            const triggerConfetti = {\n                                                \"BillingPage.useEffect.refreshWithRetry.triggerConfetti\": ()=>{\n                                                    (0,canvas_confetti__WEBPACK_IMPORTED_MODULE_8__[\"default\"])({\n                                                        particleCount: 100,\n                                                        spread: 70,\n                                                        origin: {\n                                                            y: 0.6\n                                                        }\n                                                    });\n                                                }\n                                            }[\"BillingPage.useEffect.refreshWithRetry.triggerConfetti\"];\n                                            triggerConfetti();\n                                            setTimeout(triggerConfetti, 500);\n                                            setTimeout(triggerConfetti, 1000);\n                                        } else {\n                                            // Show generic success message for downgrades/cancellations\n                                            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success('Billing settings updated successfully!');\n                                        }\n                                        console.log('Plan change detected and processed successfully');\n                                        break;\n                                    } else if (attempts >= 3) {\n                                        // After a few attempts, show success message even if no change detected\n                                        // (user might have just viewed the portal without making changes)\n                                        sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success('Billing settings updated successfully!');\n                                        console.log('No plan change detected, but showing success message');\n                                        break;\n                                    }\n                                }\n                                attempts++;\n                                if (attempts < maxAttempts) {\n                                    console.log(\"No change detected yet, waiting \".concat(retryDelay, \"ms before next attempt...\"));\n                                    await new Promise({\n                                        \"BillingPage.useEffect.refreshWithRetry\": (resolve)=>setTimeout(resolve, retryDelay)\n                                    }[\"BillingPage.useEffect.refreshWithRetry\"]);\n                                }\n                            } catch (error) {\n                                console.error(\"Refresh attempt \".concat(attempts + 1, \" failed:\"), error);\n                                attempts++;\n                                if (attempts >= maxAttempts) {\n                                    sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error('There was an issue updating your subscription status. Please refresh the page.');\n                                } else {\n                                    await new Promise({\n                                        \"BillingPage.useEffect.refreshWithRetry\": (resolve)=>setTimeout(resolve, retryDelay)\n                                    }[\"BillingPage.useEffect.refreshWithRetry\"]);\n                                }\n                            }\n                        }\n                    }\n                }[\"BillingPage.useEffect.refreshWithRetry\"];\n                refreshWithRetry();\n            }\n        }\n    }[\"BillingPage.useEffect\"], [\n        searchParams,\n        refreshSubscription,\n        user === null || user === void 0 ? void 0 : user.id\n    ]); // Removed subscriptionStatus?.tier from dependencies\n    const handleChangePlan = async ()=>{\n        // For existing users (anyone who can access the billing page), always use Customer Portal\n        // The billing page is only accessible to authenticated users with accounts\n        try {\n            setLoading(true);\n            // Create return URL with current domain, portal return parameter, and current tier\n            const currentTier = (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) || 'free';\n            const returnUrl = \"\".concat(window.location.origin, \"/billing?portal_return=true&prev_tier=\").concat(currentTier);\n            await openCustomerPortal(returnUrl);\n        } catch (error) {\n            console.error('Customer portal error:', error);\n            // If portal is not configured, fall back to showing a helpful message\n            if (error.message.includes('No configuration provided') || error.message.includes('default configuration has not been created')) {\n                sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error('Billing portal is being set up. Please contact support for plan changes.');\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error('Failed to open billing portal. Please try again.');\n            }\n            setLoading(false);\n        }\n    };\n    const handleCancelSubscription = async ()=>{\n        if (!cancelReason.trim()) {\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error('Please select a reason for cancellation');\n            return;\n        }\n        setLoading(true);\n        try {\n            var _user_user_metadata;\n            // Send cancellation feedback via EmailJS\n            const templateParams = {\n                user_email: (user === null || user === void 0 ? void 0 : user.email) || 'Unknown',\n                user_name: (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.first_name) || 'User',\n                current_plan: currentPlan.name,\n                cancel_reason: cancelReason,\n                additional_feedback: cancelFeedback,\n                cancel_date: new Date().toLocaleDateString()\n            };\n            await _emailjs_browser__WEBPACK_IMPORTED_MODULE_10__[\"default\"].send(\"service_2xtn7iv\", \"template_pg7e1af\", templateParams, \"lm7-ATth2Cql60KIN\");\n            // Here you would also cancel the subscription in your payment processor\n            // For now, we'll simulate the process\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success('Subscription cancelled successfully. We\\'ve sent your feedback to our team.');\n            setShowCancelModal(false);\n            setCancelReason('');\n            setCancelFeedback('');\n            await refreshSubscription();\n        } catch (error) {\n            console.error('Cancellation error:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error('Failed to cancel subscription. Please contact support.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const cancelReasons = [\n        'Too expensive',\n        'Not using enough features',\n        'Found a better alternative',\n        'Technical issues',\n        'Poor customer support',\n        'Missing features I need',\n        'Temporary financial constraints',\n        'Other'\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-slide-in\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-2\",\n                                    children: \"Billing & Plans \\uD83D\\uDCB3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 text-lg\",\n                                    children: \"Manage your subscription, billing information, and plan features.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            onClick: refreshSubscription,\n                            className: \"text-gray-600 border-gray-300 hover:bg-gray-50\",\n                            size: \"sm\",\n                            children: \"\\uD83D\\uDD04 Refresh Status\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                    lineNumber: 327,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                lineNumber: 326,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card p-8 hover:shadow-lg transition-all duration-200 animate-slide-in\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 bg-orange-100 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-6 w-6 text-orange-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-900\",\n                                children: \"Current Plan\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-3xl font-bold text-gray-900\",\n                                                children: currentPlan.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 15\n                                            }, this),\n                                            currentPlan.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                className: \"bg-gradient-to-r from-orange-500 to-orange-600 text-white px-3 py-1 text-sm font-semibold\",\n                                                children: \"⭐ Popular\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl font-semibold text-gray-700\",\n                                                children: currentPlan.price === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600\",\n                                                    children: \"Free forever\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"$\",\n                                                        currentPlan.price,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500 font-normal\",\n                                                            children: [\n                                                                \"/\",\n                                                                currentPlan.interval\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 15\n                                            }, this),\n                                            daysUntilRenewal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 text-sm text-gray-500 bg-gray-50 px-3 py-2 rounded-lg inline-flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Renews in \",\n                                                            daysUntilRenewal,\n                                                            \" days\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) !== 'free' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowCancelModal(true),\n                                    className: \"text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300 transition-all duration-200\",\n                                    children: \"Cancel Subscription\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                lineNumber: 348,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-slide-in\",\n                style: {\n                    animationDelay: '200ms'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"Plan Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-px bg-gradient-to-r from-orange-200 to-transparent flex-1 ml-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-2xl mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card relative overflow-hidden transition-all duration-300 hover:shadow-xl\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"inline-flex items-center gap-2 bg-green-100 text-green-800 px-4 py-2 rounded-full text-sm font-semibold mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Current Plan\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                                children: currentPlan.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-5xl font-bold text-gray-900\",\n                                                        children: [\n                                                            \"$\",\n                                                            currentPlan.price\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    currentPlan.price > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500 text-xl\",\n                                                        children: [\n                                                            \"/\",\n                                                            currentPlan.interval\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 17\n                                            }, this),\n                                            currentPlan.price === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-green-600 font-semibold text-lg\",\n                                                children: \"Forever free\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                children: \"Your Current Features:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                                                children: currentPlan.features.slice(0, 6).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-shrink-0\",\n                                                                children: feature.included ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-1 bg-green-100 rounded-full\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                        lineNumber: 434,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 27\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-1 bg-gray-100 rounded-full\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                        lineNumber: 438,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                    lineNumber: 437,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 431,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm \".concat(feature.included ? 'text-gray-900' : 'text-gray-400'),\n                                                                children: [\n                                                                    feature.name,\n                                                                    feature.limit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500 font-medium\",\n                                                                        children: [\n                                                                            \" (\",\n                                                                            feature.limit,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                        lineNumber: 447,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: handleChangePlan,\n                                                disabled: loading,\n                                                className: \"w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg hover:shadow-xl transition-all duration-200\",\n                                                size: \"lg\",\n                                                children: (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Choose a Paid Plan\"\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Change Plan or Cancel\"\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-center text-sm text-gray-500\",\n                                                children: (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' ? 'Upgrade to unlock more features and higher limits' : 'Manage your subscription, change plans, or cancel anytime'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                lineNumber: 397,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 animate-slide-in\",\n                style: {\n                    animationDelay: '400ms'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-blue-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-6 w-6 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"Billing Information\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 492,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center py-3 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Current Plan\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-gray-900\",\n                                                children: currentPlan.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center py-3 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Billing Cycle\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-gray-900\",\n                                                children: currentPlan.price === 0 ? 'N/A' : \"Monthly (\".concat(currentPlan.interval, \")\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 13\n                                    }, this),\n                                    daysUntilRenewal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center py-3 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Next Billing Date\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-gray-900\",\n                                                children: new Date(Date.now() + daysUntilRenewal * 24 * 60 * 60 * 1000).toLocaleDateString()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center py-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                className: \"\".concat((subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' ? 'bg-gray-100 text-gray-800' : 'bg-green-100 text-green-800'),\n                                                children: (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' ? 'Free Plan' : 'Active'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 498,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 491,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-purple-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-6 w-6 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 533,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"Need Help?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Have questions about your subscription or need assistance with billing?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 539,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            className: \"w-full justify-start\",\n                                            onClick: ()=>window.open('/contact', '_blank'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Contact Support\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 542,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 538,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 531,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                lineNumber: 489,\n                columnNumber: 7\n            }, this),\n            showCancelModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-2xl max-w-md w-full p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-6 w-6 text-red-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 561,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Cancel Subscription\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 562,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 560,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"We're sorry to see you go! Please help us improve by telling us why you're cancelling.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 565,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Reason for cancellation *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 571,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: cancelReason,\n                                            onChange: (e)=>setCancelReason(e.target.value),\n                                            className: \"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select a reason...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 19\n                                                }, this),\n                                                cancelReasons.map((reason)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: reason,\n                                                        children: reason\n                                                    }, reason, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 570,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Additional feedback (optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 587,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: cancelFeedback,\n                                            onChange: (e)=>setCancelFeedback(e.target.value),\n                                            placeholder: \"Tell us more about your experience or what we could do better...\",\n                                            rows: 3,\n                                            className: \"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 590,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 586,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 569,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowCancelModal(false),\n                                    className: \"flex-1\",\n                                    children: \"Keep Subscription\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleCancelSubscription,\n                                    disabled: loading || !cancelReason.trim(),\n                                    className: \"flex-1 bg-red-600 hover:bg-red-700\",\n                                    children: loading ? 'Cancelling...' : 'Cancel Subscription'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 608,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 600,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                    lineNumber: 559,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                lineNumber: 558,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: confirmation.isOpen,\n                onClose: confirmation.hideConfirmation,\n                onConfirm: confirmation.onConfirm,\n                title: confirmation.title,\n                message: confirmation.message,\n                confirmText: confirmation.confirmText,\n                cancelText: confirmation.cancelText,\n                type: confirmation.type,\n                isLoading: confirmation.isLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                lineNumber: 621,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n        lineNumber: 324,\n        columnNumber: 5\n    }, this);\n}\n_s(BillingPage, \"ao9wCuBkrny2/ktyGsoTqY438I0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_6__.useSubscription,\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_9__.useConfirmation\n    ];\n});\n_c = BillingPage;\nvar _c;\n$RefreshReg$(_c, \"BillingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/billing/page.tsx\n"));

/***/ })

});