"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/billing/page",{

/***/ "(app-pages-browser)/./src/app/billing/page.tsx":
/*!**********************************!*\
  !*** ./src/app/billing/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BillingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CreditCardIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var _components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/ConfirmationModal */ \"(app-pages-browser)/./src/components/ui/ConfirmationModal.tsx\");\n/* harmony import */ var _components_DebugSubscriptionStatus__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/DebugSubscriptionStatus */ \"(app-pages-browser)/./src/components/DebugSubscriptionStatus.tsx\");\n/* harmony import */ var _components_DebugWebhookLogs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/DebugWebhookLogs */ \"(app-pages-browser)/./src/components/DebugWebhookLogs.tsx\");\n/* harmony import */ var canvas_confetti__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! canvas-confetti */ \"(app-pages-browser)/./node_modules/canvas-confetti/dist/confetti.module.mjs\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* harmony import */ var _emailjs_browser__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @emailjs/browser */ \"(app-pages-browser)/./node_modules/@emailjs/browser/es/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst plans = [\n    {\n        id: 'free',\n        name: 'Free',\n        price: 0,\n        interval: 'forever',\n        features: [\n            {\n                name: 'Strict fallback routing only',\n                included: true\n            },\n            {\n                name: 'Basic analytics',\n                included: true\n            },\n            {\n                name: 'No custom roles',\n                included: false\n            },\n            {\n                name: 'Configurations',\n                included: true,\n                limit: '1 max'\n            },\n            {\n                name: 'API keys per config',\n                included: true,\n                limit: '3 max'\n            },\n            {\n                name: 'User-generated API keys',\n                included: true,\n                limit: '3 max'\n            }\n        ]\n    },\n    {\n        id: 'starter',\n        name: 'Starter',\n        price: 19,\n        interval: 'month',\n        popular: true,\n        features: [\n            {\n                name: 'All routing strategies',\n                included: true\n            },\n            {\n                name: 'Advanced analytics',\n                included: true\n            },\n            {\n                name: 'Custom roles',\n                included: true,\n                limit: 'Up to 3 roles'\n            },\n            {\n                name: 'Configurations',\n                included: true,\n                limit: '5 max'\n            },\n            {\n                name: 'API keys per config',\n                included: true,\n                limit: '15 max'\n            },\n            {\n                name: 'User-generated API keys',\n                included: true,\n                limit: '50 max'\n            },\n            {\n                name: 'Browsing tasks',\n                included: true,\n                limit: '15/month'\n            }\n        ]\n    },\n    {\n        id: 'professional',\n        name: 'Professional',\n        price: 49,\n        interval: 'month',\n        features: [\n            {\n                name: 'Everything in Starter',\n                included: true\n            },\n            {\n                name: 'Unlimited configurations',\n                included: true\n            },\n            {\n                name: 'Unlimited API keys per config',\n                included: true\n            },\n            {\n                name: 'Unlimited user-generated API keys',\n                included: true\n            },\n            {\n                name: 'Knowledge base documents',\n                included: true,\n                limit: '5 documents'\n            },\n            {\n                name: 'Priority support',\n                included: true\n            }\n        ]\n    },\n    {\n        id: 'enterprise',\n        name: 'Enterprise',\n        price: 149,\n        interval: 'month',\n        features: [\n            {\n                name: 'Everything in Professional',\n                included: true\n            },\n            {\n                name: 'Unlimited knowledge base documents',\n                included: true\n            },\n            {\n                name: 'Advanced semantic caching',\n                included: true\n            },\n            {\n                name: 'Custom integrations',\n                included: true\n            },\n            {\n                name: 'Dedicated support + phone',\n                included: true\n            },\n            {\n                name: 'SLA guarantee',\n                included: true\n            }\n        ]\n    }\n];\nfunction BillingPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { user, subscriptionStatus, refreshStatus: refreshSubscription, createCheckoutSession, openCustomerPortal } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_6__.useSubscription)();\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_11__.useConfirmation)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCancelModal, setShowCancelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cancelReason, setCancelReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [cancelFeedback, setCancelFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tierBeforePortal, setTierBeforePortal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const currentPlan = plans.find((plan)=>plan.id === (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier)) || plans[0];\n    // Calculate days until renewal based on actual subscription data\n    const daysUntilRenewal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"BillingPage.useMemo[daysUntilRenewal]\": ()=>{\n            if ((subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' || !(subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.currentPeriodEnd)) {\n                return null;\n            }\n            const renewalDate = new Date(subscriptionStatus.currentPeriodEnd);\n            const today = new Date();\n            const diffTime = renewalDate.getTime() - today.getTime();\n            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n            // Return null if subscription has already expired\n            return diffDays > 0 ? diffDays : null;\n        }\n    }[\"BillingPage.useMemo[daysUntilRenewal]\"], [\n        subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier,\n        subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.currentPeriodEnd\n    ]);\n    // Helper function to determine if a tier change is an upgrade\n    const isUpgrade = (fromTier, toTier)=>{\n        const tierOrder = {\n            'free': 0,\n            'starter': 1,\n            'professional': 2,\n            'enterprise': 3\n        };\n        return tierOrder[toTier] > tierOrder[fromTier];\n    };\n    // Handle return from Customer Portal\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BillingPage.useEffect\": ()=>{\n            const portalReturn = searchParams.get('portal_return');\n            const previousTier = searchParams.get('prev_tier');\n            if (portalReturn === 'true') {\n                console.log('Returned from Customer Portal, previous tier:', previousTier);\n                // Remove the parameters from URL\n                const newUrl = new URL(window.location.href);\n                newUrl.searchParams.delete('portal_return');\n                newUrl.searchParams.delete('prev_tier');\n                window.history.replaceState({}, '', newUrl.toString());\n                // Wait for webhook to process, then refresh subscription status\n                const refreshWithRetry = {\n                    \"BillingPage.useEffect.refreshWithRetry\": async ()=>{\n                        let attempts = 0;\n                        const maxAttempts = 8; // Increased attempts\n                        const initialDelay = 3000; // Longer initial delay for webhook processing\n                        const retryDelay = 2000; // 2 seconds between retry attempts\n                        console.log('Starting subscription refresh with retry logic...');\n                        // Initial delay to allow webhook processing\n                        await new Promise({\n                            \"BillingPage.useEffect.refreshWithRetry\": (resolve)=>setTimeout(resolve, initialDelay)\n                        }[\"BillingPage.useEffect.refreshWithRetry\"]);\n                        while(attempts < maxAttempts){\n                            try {\n                                console.log(\"Refresh attempt \".concat(attempts + 1, \"/\").concat(maxAttempts));\n                                // Force a complete refresh\n                                await refreshSubscription();\n                                // Wait a moment for the state to update\n                                await new Promise({\n                                    \"BillingPage.useEffect.refreshWithRetry\": (resolve)=>setTimeout(resolve, 500)\n                                }[\"BillingPage.useEffect.refreshWithRetry\"]);\n                                // Get fresh subscription status after refresh\n                                const response = await fetch(\"/api/stripe/subscription-status?userId=\".concat(user === null || user === void 0 ? void 0 : user.id, \"&_t=\").concat(Date.now()), {\n                                    cache: 'no-store',\n                                    headers: {\n                                        'Cache-Control': 'no-cache, no-store, must-revalidate',\n                                        'Pragma': 'no-cache',\n                                        'Expires': '0'\n                                    }\n                                });\n                                if (response.ok) {\n                                    const freshStatus = await response.json();\n                                    const currentTier = freshStatus.tier || 'free';\n                                    console.log('Fresh tier from API:', currentTier, 'Previous tier:', previousTier);\n                                    // Check if tier actually changed\n                                    if (currentTier !== previousTier) {\n                                        const wasUpgrade = isUpgrade(previousTier || 'free', currentTier);\n                                        if (wasUpgrade) {\n                                            // Show upgrade success message with confetti\n                                            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success('Plan upgraded successfully!');\n                                            // Trigger confetti for upgrades only\n                                            const triggerConfetti = {\n                                                \"BillingPage.useEffect.refreshWithRetry.triggerConfetti\": ()=>{\n                                                    (0,canvas_confetti__WEBPACK_IMPORTED_MODULE_10__[\"default\"])({\n                                                        particleCount: 100,\n                                                        spread: 70,\n                                                        origin: {\n                                                            y: 0.6\n                                                        }\n                                                    });\n                                                }\n                                            }[\"BillingPage.useEffect.refreshWithRetry.triggerConfetti\"];\n                                            triggerConfetti();\n                                            setTimeout(triggerConfetti, 500);\n                                            setTimeout(triggerConfetti, 1000);\n                                        } else {\n                                            // Show generic success message for downgrades/cancellations\n                                            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success('Billing settings updated successfully!');\n                                        }\n                                        console.log('Plan change detected and processed successfully');\n                                        break;\n                                    } else if (attempts >= 3) {\n                                        // After a few attempts, show success message even if no change detected\n                                        // (user might have just viewed the portal without making changes)\n                                        sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success('Billing settings updated successfully!');\n                                        console.log('No plan change detected, but showing success message');\n                                        break;\n                                    }\n                                }\n                                attempts++;\n                                if (attempts < maxAttempts) {\n                                    console.log(\"No change detected yet, waiting \".concat(retryDelay, \"ms before next attempt...\"));\n                                    await new Promise({\n                                        \"BillingPage.useEffect.refreshWithRetry\": (resolve)=>setTimeout(resolve, retryDelay)\n                                    }[\"BillingPage.useEffect.refreshWithRetry\"]);\n                                }\n                            } catch (error) {\n                                console.error(\"Refresh attempt \".concat(attempts + 1, \" failed:\"), error);\n                                attempts++;\n                                if (attempts >= maxAttempts) {\n                                    console.log('Max refresh attempts reached, forcing page reload...');\n                                    sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error('Refreshing page to update subscription status...');\n                                    // Force a page reload as last resort\n                                    setTimeout({\n                                        \"BillingPage.useEffect.refreshWithRetry\": ()=>{\n                                            window.location.reload();\n                                        }\n                                    }[\"BillingPage.useEffect.refreshWithRetry\"], 2000);\n                                } else {\n                                    await new Promise({\n                                        \"BillingPage.useEffect.refreshWithRetry\": (resolve)=>setTimeout(resolve, retryDelay)\n                                    }[\"BillingPage.useEffect.refreshWithRetry\"]);\n                                }\n                            }\n                        }\n                    }\n                }[\"BillingPage.useEffect.refreshWithRetry\"];\n                refreshWithRetry();\n            }\n        }\n    }[\"BillingPage.useEffect\"], [\n        searchParams,\n        refreshSubscription,\n        user === null || user === void 0 ? void 0 : user.id\n    ]); // Removed subscriptionStatus?.tier from dependencies\n    const handleChangePlan = async ()=>{\n        // For existing users (anyone who can access the billing page), always use Customer Portal\n        // The billing page is only accessible to authenticated users with accounts\n        try {\n            setLoading(true);\n            // Create return URL with current domain, portal return parameter, and current tier\n            const currentTier = (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) || 'free';\n            const returnUrl = \"\".concat(window.location.origin, \"/billing?portal_return=true&prev_tier=\").concat(currentTier);\n            await openCustomerPortal(returnUrl);\n        } catch (error) {\n            console.error('Customer portal error:', error);\n            // If portal is not configured, fall back to showing a helpful message\n            if (error.message.includes('No configuration provided') || error.message.includes('default configuration has not been created')) {\n                sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error('Billing portal is being set up. Please contact support for plan changes.');\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error('Failed to open billing portal. Please try again.');\n            }\n            setLoading(false);\n        }\n    };\n    const handleCancelSubscription = async ()=>{\n        if (!cancelReason.trim()) {\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error('Please select a reason for cancellation');\n            return;\n        }\n        setLoading(true);\n        try {\n            var _user_user_metadata;\n            // Send cancellation feedback via EmailJS\n            const templateParams = {\n                user_email: (user === null || user === void 0 ? void 0 : user.email) || 'Unknown',\n                user_name: (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.first_name) || 'User',\n                current_plan: currentPlan.name,\n                cancel_reason: cancelReason,\n                additional_feedback: cancelFeedback,\n                cancel_date: new Date().toLocaleDateString()\n            };\n            await _emailjs_browser__WEBPACK_IMPORTED_MODULE_12__[\"default\"].send(\"service_2xtn7iv\", \"template_pg7e1af\", templateParams, \"lm7-ATth2Cql60KIN\");\n            // Here you would also cancel the subscription in your payment processor\n            // For now, we'll simulate the process\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success('Subscription cancelled successfully. We\\'ve sent your feedback to our team.');\n            setShowCancelModal(false);\n            setCancelReason('');\n            setCancelFeedback('');\n            await refreshSubscription();\n        } catch (error) {\n            console.error('Cancellation error:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error('Failed to cancel subscription. Please contact support.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const cancelReasons = [\n        'Too expensive',\n        'Not using enough features',\n        'Found a better alternative',\n        'Technical issues',\n        'Poor customer support',\n        'Missing features I need',\n        'Temporary financial constraints',\n        'Other'\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-slide-in\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-2\",\n                                    children: \"Billing & Plans \\uD83D\\uDCB3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 text-lg\",\n                                    children: \"Manage your subscription, billing information, and plan features.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            onClick: refreshSubscription,\n                            className: \"text-gray-600 border-gray-300 hover:bg-gray-50\",\n                            size: \"sm\",\n                            children: \"\\uD83D\\uDD04 Refresh Status\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card p-8 hover:shadow-lg transition-all duration-200 animate-slide-in\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 bg-orange-100 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-6 w-6 text-orange-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-900\",\n                                children: \"Current Plan\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-3xl font-bold text-gray-900\",\n                                                children: currentPlan.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 15\n                                            }, this),\n                                            currentPlan.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                className: \"bg-gradient-to-r from-orange-500 to-orange-600 text-white px-3 py-1 text-sm font-semibold\",\n                                                children: \"⭐ Popular\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl font-semibold text-gray-700\",\n                                                children: currentPlan.price === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600\",\n                                                    children: \"Free forever\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"$\",\n                                                        currentPlan.price,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500 font-normal\",\n                                                            children: [\n                                                                \"/\",\n                                                                currentPlan.interval\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 15\n                                            }, this),\n                                            daysUntilRenewal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 text-sm text-gray-500 bg-gray-50 px-3 py-2 rounded-lg inline-flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Renews in \",\n                                                            daysUntilRenewal,\n                                                            \" days\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) !== 'free' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowCancelModal(true),\n                                    className: \"text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300 transition-all duration-200\",\n                                    children: \"Cancel Subscription\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                lineNumber: 355,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-slide-in\",\n                style: {\n                    animationDelay: '200ms'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"Plan Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-px bg-gradient-to-r from-orange-200 to-transparent flex-1 ml-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-2xl mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card relative overflow-hidden transition-all duration-300 hover:shadow-xl\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"inline-flex items-center gap-2 bg-green-100 text-green-800 px-4 py-2 rounded-full text-sm font-semibold mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Current Plan\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                                children: currentPlan.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-5xl font-bold text-gray-900\",\n                                                        children: [\n                                                            \"$\",\n                                                            currentPlan.price\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    currentPlan.price > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500 text-xl\",\n                                                        children: [\n                                                            \"/\",\n                                                            currentPlan.interval\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 17\n                                            }, this),\n                                            currentPlan.price === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-green-600 font-semibold text-lg\",\n                                                children: \"Forever free\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                children: \"Your Current Features:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                                                children: currentPlan.features.slice(0, 6).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-shrink-0\",\n                                                                children: feature.included ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-1 bg-green-100 rounded-full\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                        lineNumber: 441,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                    lineNumber: 440,\n                                                                    columnNumber: 27\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-1 bg-gray-100 rounded-full\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                        lineNumber: 445,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                    lineNumber: 444,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 438,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm \".concat(feature.included ? 'text-gray-900' : 'text-gray-400'),\n                                                                children: [\n                                                                    feature.name,\n                                                                    feature.limit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500 font-medium\",\n                                                                        children: [\n                                                                            \" (\",\n                                                                            feature.limit,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                        lineNumber: 454,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: handleChangePlan,\n                                                disabled: loading,\n                                                className: \"w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg hover:shadow-xl transition-all duration-200\",\n                                                size: \"lg\",\n                                                children: (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 472,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Choose a Paid Plan\"\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Change Plan or Cancel\"\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-center text-sm text-gray-500\",\n                                                children: (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' ? 'Upgrade to unlock more features and higher limits' : 'Manage your subscription, change plans, or cancel anytime'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 411,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                lineNumber: 404,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 animate-slide-in\",\n                style: {\n                    animationDelay: '400ms'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-blue-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-6 w-6 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"Billing Information\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center py-3 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Current Plan\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-gray-900\",\n                                                children: currentPlan.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 506,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center py-3 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Billing Cycle\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-gray-900\",\n                                                children: currentPlan.price === 0 ? 'N/A' : \"Monthly (\".concat(currentPlan.interval, \")\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 13\n                                    }, this),\n                                    daysUntilRenewal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center py-3 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Next Billing Date\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-gray-900\",\n                                                children: new Date(Date.now() + daysUntilRenewal * 24 * 60 * 60 * 1000).toLocaleDateString()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center py-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                className: \"\".concat((subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' ? 'bg-gray-100 text-gray-800' : 'bg-green-100 text-green-800'),\n                                                children: (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' ? 'Free Plan' : 'Active'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 505,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 498,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-purple-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-6 w-6 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"Need Help?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 543,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 539,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Have questions about your subscription or need assistance with billing?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            className: \"w-full justify-start\",\n                                            onClick: ()=>window.open('/contact', '_blank'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Contact Support\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 549,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 545,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 538,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                lineNumber: 496,\n                columnNumber: 7\n            }, this),\n            showCancelModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-2xl max-w-md w-full p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"h-6 w-6 text-red-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Cancel Subscription\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 569,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 567,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"We're sorry to see you go! Please help us improve by telling us why you're cancelling.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 572,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Reason for cancellation *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: cancelReason,\n                                            onChange: (e)=>setCancelReason(e.target.value),\n                                            className: \"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select a reason...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 19\n                                                }, this),\n                                                cancelReasons.map((reason)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: reason,\n                                                        children: reason\n                                                    }, reason, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 588,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 581,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 577,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Additional feedback (optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 594,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: cancelFeedback,\n                                            onChange: (e)=>setCancelFeedback(e.target.value),\n                                            placeholder: \"Tell us more about your experience or what we could do better...\",\n                                            rows: 3,\n                                            className: \"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 593,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 576,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowCancelModal(false),\n                                    className: \"flex-1\",\n                                    children: \"Keep Subscription\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 608,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleCancelSubscription,\n                                    disabled: loading || !cancelReason.trim(),\n                                    className: \"flex-1 bg-red-600 hover:bg-red-700\",\n                                    children: loading ? 'Cancelling...' : 'Cancel Subscription'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 615,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 607,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                    lineNumber: 566,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                lineNumber: 565,\n                columnNumber: 9\n            }, this),\n             true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DebugSubscriptionStatus__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 630,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DebugWebhookLogs__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 631,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                lineNumber: 629,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: confirmation.isOpen,\n                onClose: confirmation.hideConfirmation,\n                onConfirm: confirmation.onConfirm,\n                title: confirmation.title,\n                message: confirmation.message,\n                confirmText: confirmation.confirmText,\n                cancelText: confirmation.cancelText,\n                type: confirmation.type,\n                isLoading: confirmation.isLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                lineNumber: 636,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n        lineNumber: 331,\n        columnNumber: 5\n    }, this);\n}\n_s(BillingPage, \"ao9wCuBkrny2/ktyGsoTqY438I0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_6__.useSubscription,\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_11__.useConfirmation\n    ];\n});\n_c = BillingPage;\nvar _c;\n$RefreshReg$(_c, \"BillingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/billing/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/DebugSubscriptionStatus.tsx":
/*!****************************************************!*\
  !*** ./src/components/DebugSubscriptionStatus.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DebugSubscriptionStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction DebugSubscriptionStatus() {\n    _s();\n    const { user } = useAuth();\n    const [debugData, setDebugData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fetchDebugData = async ()=>{\n        if (!user) {\n            setError('No user logged in');\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        try {\n            const response = await fetch(\"/api/debug/subscription-data?userId=\".concat(user.id, \"&_t=\").concat(Date.now()), {\n                cache: 'no-store',\n                headers: {\n                    'Cache-Control': 'no-cache, no-store, must-revalidate',\n                    'Pragma': 'no-cache',\n                    'Expires': '0'\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const data = await response.json();\n            setDebugData(data);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Unknown error');\n            console.error('Debug fetch error:', err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-yellow-800\",\n                children: \"Please log in to view subscription debug data.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                lineNumber: 79,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 bg-white border border-gray-200 rounded-lg shadow-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"\\uD83D\\uDD0D Subscription Debug Tool\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchDebugData,\n                        disabled: loading,\n                        className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                        children: loading ? 'Loading...' : 'Fetch Debug Data'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-800 text-sm\",\n                    children: [\n                        \"Error: \",\n                        error\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                lineNumber: 98,\n                columnNumber: 9\n            }, this),\n            debugData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-900 mb-2\",\n                                        children: \"\\uD83D\\uDC64 User Profile\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Tier:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 bg-blue-100 text-blue-800 rounded\",\n                                                        children: debugData.profile.subscription_tier\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 63\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Status:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 bg-green-100 text-green-800 rounded\",\n                                                        children: debugData.profile.subscription_status\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"User Status:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 bg-purple-100 text-purple-800 rounded\",\n                                                        children: debugData.profile.user_status\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 70\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Updated:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 20\n                                                    }, this),\n                                                    \" \",\n                                                    new Date(debugData.profile.updated_at).toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-900 mb-2\",\n                                        children: \"\\uD83C\\uDFAF Active Subscription\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, this),\n                                    debugData.active_subscription.data ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Tier:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 22\n                                                    }, this),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 bg-green-100 text-green-800 rounded\",\n                                                        children: debugData.active_subscription.data.tier\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Status:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 22\n                                                    }, this),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 bg-green-100 text-green-800 rounded\",\n                                                        children: debugData.active_subscription.data.status\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 67\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Updated:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 22\n                                                    }, this),\n                                                    \" \",\n                                                    new Date(debugData.active_subscription.data.updated_at).toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Stripe ID:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 22\n                                                    }, this),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-mono bg-gray-200 px-1 rounded\",\n                                                        children: debugData.active_subscription.data.stripe_subscription_id\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 70\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"No active subscription found\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 17\n                                    }, this),\n                                    debugData.active_subscription.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-red-600 mt-2\",\n                                        children: [\n                                            \"Error: \",\n                                            debugData.active_subscription.error\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-blue-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-semibold text-gray-900 mb-2\",\n                                children: \"\\uD83E\\uDDE0 Tier Determination Logic\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-700 mb-2\",\n                                children: debugData.tier_determination.final_tier_logic\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-5 gap-2 text-xs\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Profile:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-1 py-0.5 bg-gray-200 rounded\",\n                                                children: debugData.tier_determination.profile_tier\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Latest Sub:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-1 py-0.5 bg-gray-200 rounded\",\n                                                children: debugData.tier_determination.latest_subscription_tier || 'N/A'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Latest Status:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-1 py-0.5 bg-gray-200 rounded\",\n                                                children: debugData.tier_determination.latest_subscription_status || 'N/A'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Active Sub:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-1 py-0.5 bg-gray-200 rounded\",\n                                                children: debugData.tier_determination.active_subscription_tier || 'N/A'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Active Status:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-1 py-0.5 bg-gray-200 rounded\",\n                                                children: debugData.tier_determination.active_subscription_status || 'N/A'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-gray-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-semibold text-gray-900 mb-2\",\n                                children: [\n                                    \"\\uD83D\\uDCCB All Subscriptions (\",\n                                    debugData.subscriptions.total_count,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this),\n                            debugData.subscriptions.all_subscriptions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: debugData.subscriptions.all_subscriptions.map((sub, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-white border rounded text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    \"#\",\n                                                                    index + 1\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                                lineNumber: 178,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \" -\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 bg-blue-100 text-blue-800 rounded ml-1\",\n                                                                children: sub.tier\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                                lineNumber: 179,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 rounded ml-1 \".concat(sub.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                                children: sub.status\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            \"Updated: \",\n                                                            new Date(sub.updated_at).toLocaleString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-600 mt-1 font-mono\",\n                                                children: sub.stripe_subscription_id\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, sub.id, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"No subscriptions found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-500 text-center\",\n                        children: [\n                            \"Last fetched: \",\n                            new Date(debugData.timestamp).toLocaleString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n                lineNumber: 104,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugSubscriptionStatus.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\n_s(DebugSubscriptionStatus, \"/09dTTLiaMnnL30M4NqhPb3tEpg=\", true);\n_c = DebugSubscriptionStatus;\nvar _c;\n$RefreshReg$(_c, \"DebugSubscriptionStatus\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DebugSubscriptionStatus.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/DebugWebhookLogs.tsx":
/*!*********************************************!*\
  !*** ./src/components/DebugWebhookLogs.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DebugWebhookLogs)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction DebugWebhookLogs() {\n    _s();\n    const [logs, setLogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [autoRefresh, setAutoRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fetchLogs = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            const response = await fetch('/api/debug/webhook-logs', {\n                cache: 'no-store',\n                headers: {\n                    'Cache-Control': 'no-cache, no-store, must-revalidate',\n                    'Pragma': 'no-cache',\n                    'Expires': '0'\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const data = await response.json();\n            setLogs(data);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Unknown error');\n            console.error('Webhook logs fetch error:', err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const clearLogs = async ()=>{\n        try {\n            const response = await fetch('/api/debug/webhook-logs', {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                setLogs({\n                    total: 0,\n                    recent: [],\n                    lastUpdated: new Date().toISOString()\n                });\n            }\n        } catch (err) {\n            console.error('Failed to clear logs:', err);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DebugWebhookLogs.useEffect\": ()=>{\n            fetchLogs();\n        }\n    }[\"DebugWebhookLogs.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DebugWebhookLogs.useEffect\": ()=>{\n            if (autoRefresh) {\n                const interval = setInterval(fetchLogs, 5000); // Refresh every 5 seconds\n                return ({\n                    \"DebugWebhookLogs.useEffect\": ()=>clearInterval(interval)\n                })[\"DebugWebhookLogs.useEffect\"];\n            }\n        }\n    }[\"DebugWebhookLogs.useEffect\"], [\n        autoRefresh\n    ]);\n    const getEventTypeColor = (eventType)=>{\n        switch(eventType){\n            case 'customer.subscription.updated':\n                return 'bg-blue-100 text-blue-800';\n            case 'customer.subscription.created':\n                return 'bg-green-100 text-green-800';\n            case 'customer.subscription.deleted':\n                return 'bg-red-100 text-red-800';\n            case 'checkout.session.completed':\n                return 'bg-purple-100 text-purple-800';\n            case 'invoice.payment_succeeded':\n                return 'bg-green-100 text-green-800';\n            case 'invoice.payment_failed':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 bg-white border border-gray-200 rounded-lg shadow-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"\\uD83D\\uDD17 Webhook Event Logs\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center space-x-2 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: autoRefresh,\n                                        onChange: (e)=>setAutoRefresh(e.target.checked),\n                                        className: \"rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Auto-refresh\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: fetchLogs,\n                                disabled: loading,\n                                className: \"px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50\",\n                                children: loading ? 'Loading...' : 'Refresh'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: clearLogs,\n                                className: \"px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700\",\n                                children: \"Clear\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-800 text-sm\",\n                    children: [\n                        \"Error: \",\n                        error\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, this),\n            logs && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center text-sm text-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Total events: \",\n                                    logs.total\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Last updated: \",\n                                    new Date(logs.lastUpdated).toLocaleString()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this),\n                    logs.recent.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 max-h-96 overflow-y-auto\",\n                        children: logs.recent.map((log)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 bg-gray-50 border rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded text-xs font-medium \".concat(getEventTypeColor(log.eventType)),\n                                                        children: log.eventType\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    log.tier && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-xs font-medium\",\n                                                        children: log.tier\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    log.status && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded text-xs font-medium \".concat(log.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                        children: log.status\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: new Date(log.timestamp).toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-2 text-xs\",\n                                        children: [\n                                            log.subscriptionId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Subscription:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-mono bg-gray-200 px-1 rounded\",\n                                                        children: log.subscriptionId\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 23\n                                            }, this),\n                                            log.customerId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Customer:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-mono bg-gray-200 px-1 rounded\",\n                                                        children: log.customerId\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 23\n                                            }, this),\n                                            log.priceId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Price ID:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-mono bg-gray-200 px-1 rounded\",\n                                                        children: log.priceId\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 19\n                                    }, this),\n                                    log.details && Object.keys(log.details).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                        className: \"mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                className: \"text-xs text-gray-600 cursor-pointer hover:text-gray-800\",\n                                                children: \"View details\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"mt-1 text-xs bg-gray-100 p-2 rounded overflow-x-auto\",\n                                                children: JSON.stringify(log.details, null, 2)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, log.id, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8 text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No webhook events logged yet.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-1\",\n                                children: \"Events will appear here when webhooks are received.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n                lineNumber: 138,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DebugWebhookLogs.tsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, this);\n}\n_s(DebugWebhookLogs, \"djCGCC1NbhRDzmMVLf8Z0w60lfY=\");\n_c = DebugWebhookLogs;\nvar _c;\n$RefreshReg$(_c, \"DebugWebhookLogs\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DebugWebhookLogs.tsx\n"));

/***/ })

});