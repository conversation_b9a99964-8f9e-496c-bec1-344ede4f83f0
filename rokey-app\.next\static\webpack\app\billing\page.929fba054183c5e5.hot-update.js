"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/billing/page",{

/***/ "(app-pages-browser)/./src/app/billing/page.tsx":
/*!**********************************!*\
  !*** ./src/app/billing/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BillingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CreditCardIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var _components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/ConfirmationModal */ \"(app-pages-browser)/./src/components/ui/ConfirmationModal.tsx\");\n/* harmony import */ var canvas_confetti__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! canvas-confetti */ \"(app-pages-browser)/./node_modules/canvas-confetti/dist/confetti.module.mjs\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* harmony import */ var _emailjs_browser__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @emailjs/browser */ \"(app-pages-browser)/./node_modules/@emailjs/browser/es/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst plans = [\n    {\n        id: 'free',\n        name: 'Free',\n        price: 0,\n        interval: 'forever',\n        features: [\n            {\n                name: 'Strict fallback routing only',\n                included: true\n            },\n            {\n                name: 'Basic analytics',\n                included: true\n            },\n            {\n                name: 'No custom roles',\n                included: false\n            },\n            {\n                name: 'Configurations',\n                included: true,\n                limit: '1 max'\n            },\n            {\n                name: 'API keys per config',\n                included: true,\n                limit: '3 max'\n            },\n            {\n                name: 'User-generated API keys',\n                included: true,\n                limit: '3 max'\n            }\n        ]\n    },\n    {\n        id: 'starter',\n        name: 'Starter',\n        price: 19,\n        interval: 'month',\n        popular: true,\n        features: [\n            {\n                name: 'All routing strategies',\n                included: true\n            },\n            {\n                name: 'Advanced analytics',\n                included: true\n            },\n            {\n                name: 'Custom roles',\n                included: true,\n                limit: 'Up to 3 roles'\n            },\n            {\n                name: 'Configurations',\n                included: true,\n                limit: '5 max'\n            },\n            {\n                name: 'API keys per config',\n                included: true,\n                limit: '15 max'\n            },\n            {\n                name: 'User-generated API keys',\n                included: true,\n                limit: '50 max'\n            },\n            {\n                name: 'Browsing tasks',\n                included: true,\n                limit: '15/month'\n            }\n        ]\n    },\n    {\n        id: 'professional',\n        name: 'Professional',\n        price: 49,\n        interval: 'month',\n        features: [\n            {\n                name: 'Everything in Starter',\n                included: true\n            },\n            {\n                name: 'Unlimited configurations',\n                included: true\n            },\n            {\n                name: 'Unlimited API keys per config',\n                included: true\n            },\n            {\n                name: 'Unlimited user-generated API keys',\n                included: true\n            },\n            {\n                name: 'Knowledge base documents',\n                included: true,\n                limit: '5 documents'\n            },\n            {\n                name: 'Priority support',\n                included: true\n            }\n        ]\n    },\n    {\n        id: 'enterprise',\n        name: 'Enterprise',\n        price: 149,\n        interval: 'month',\n        features: [\n            {\n                name: 'Everything in Professional',\n                included: true\n            },\n            {\n                name: 'Unlimited knowledge base documents',\n                included: true\n            },\n            {\n                name: 'Advanced semantic caching',\n                included: true\n            },\n            {\n                name: 'Custom integrations',\n                included: true\n            },\n            {\n                name: 'Dedicated support + phone',\n                included: true\n            },\n            {\n                name: 'SLA guarantee',\n                included: true\n            }\n        ]\n    }\n];\nfunction BillingPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { user, subscriptionStatus, refreshStatus: refreshSubscription, createCheckoutSession, openCustomerPortal } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_6__.useSubscription)();\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_9__.useConfirmation)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCancelModal, setShowCancelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cancelReason, setCancelReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [cancelFeedback, setCancelFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tierBeforePortal, setTierBeforePortal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const currentPlan = plans.find((plan)=>plan.id === (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier)) || plans[0];\n    // Calculate days until renewal based on actual subscription data\n    const daysUntilRenewal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"BillingPage.useMemo[daysUntilRenewal]\": ()=>{\n            if ((subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' || !(subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.currentPeriodEnd)) {\n                return null;\n            }\n            const renewalDate = new Date(subscriptionStatus.currentPeriodEnd);\n            const today = new Date();\n            const diffTime = renewalDate.getTime() - today.getTime();\n            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n            // Return null if subscription has already expired\n            return diffDays > 0 ? diffDays : null;\n        }\n    }[\"BillingPage.useMemo[daysUntilRenewal]\"], [\n        subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier,\n        subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.currentPeriodEnd\n    ]);\n    // Helper function to determine if a tier change is an upgrade\n    const isUpgrade = (fromTier, toTier)=>{\n        const tierOrder = {\n            'free': 0,\n            'starter': 1,\n            'professional': 2,\n            'enterprise': 3\n        };\n        return tierOrder[toTier] > tierOrder[fromTier];\n    };\n    // Handle return from Customer Portal\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BillingPage.useEffect\": ()=>{\n            const portalReturn = searchParams.get('portal_return');\n            const previousTier = searchParams.get('prev_tier');\n            if (portalReturn === 'true') {\n                console.log('Returned from Customer Portal, previous tier:', previousTier);\n                // Remove the parameters from URL\n                const newUrl = new URL(window.location.href);\n                newUrl.searchParams.delete('portal_return');\n                newUrl.searchParams.delete('prev_tier');\n                window.history.replaceState({}, '', newUrl.toString());\n                // Wait for webhook to process, then refresh subscription status\n                const refreshWithRetry = {\n                    \"BillingPage.useEffect.refreshWithRetry\": async ()=>{\n                        let attempts = 0;\n                        const maxAttempts = 8; // Increased attempts\n                        const initialDelay = 3000; // Longer initial delay for webhook processing\n                        const retryDelay = 2000; // 2 seconds between retry attempts\n                        console.log('Starting subscription refresh with retry logic...');\n                        // Initial delay to allow webhook processing\n                        await new Promise({\n                            \"BillingPage.useEffect.refreshWithRetry\": (resolve)=>setTimeout(resolve, initialDelay)\n                        }[\"BillingPage.useEffect.refreshWithRetry\"]);\n                        while(attempts < maxAttempts){\n                            try {\n                                console.log(\"Refresh attempt \".concat(attempts + 1, \"/\").concat(maxAttempts));\n                                // Force a complete refresh\n                                await refreshSubscription();\n                                // Wait a moment for the state to update\n                                await new Promise({\n                                    \"BillingPage.useEffect.refreshWithRetry\": (resolve)=>setTimeout(resolve, 500)\n                                }[\"BillingPage.useEffect.refreshWithRetry\"]);\n                                // Get fresh subscription status after refresh\n                                const response = await fetch(\"/api/stripe/subscription-status?userId=\".concat(user === null || user === void 0 ? void 0 : user.id, \"&_t=\").concat(Date.now()), {\n                                    cache: 'no-store',\n                                    headers: {\n                                        'Cache-Control': 'no-cache, no-store, must-revalidate',\n                                        'Pragma': 'no-cache',\n                                        'Expires': '0'\n                                    }\n                                });\n                                if (response.ok) {\n                                    const freshStatus = await response.json();\n                                    const currentTier = freshStatus.tier || 'free';\n                                    console.log('Fresh tier from API:', currentTier, 'Previous tier:', previousTier);\n                                    // Check if tier actually changed\n                                    if (currentTier !== previousTier) {\n                                        const wasUpgrade = isUpgrade(previousTier || 'free', currentTier);\n                                        if (wasUpgrade) {\n                                            // Show upgrade success message with confetti\n                                            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success('Plan upgraded successfully!');\n                                            // Trigger confetti for upgrades only\n                                            const triggerConfetti = {\n                                                \"BillingPage.useEffect.refreshWithRetry.triggerConfetti\": ()=>{\n                                                    (0,canvas_confetti__WEBPACK_IMPORTED_MODULE_8__[\"default\"])({\n                                                        particleCount: 100,\n                                                        spread: 70,\n                                                        origin: {\n                                                            y: 0.6\n                                                        }\n                                                    });\n                                                }\n                                            }[\"BillingPage.useEffect.refreshWithRetry.triggerConfetti\"];\n                                            triggerConfetti();\n                                            setTimeout(triggerConfetti, 500);\n                                            setTimeout(triggerConfetti, 1000);\n                                        } else {\n                                            // Show generic success message for downgrades/cancellations\n                                            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success('Billing settings updated successfully!');\n                                        }\n                                        console.log('Plan change detected and processed successfully');\n                                        break;\n                                    } else if (attempts >= 3) {\n                                        // After a few attempts, show success message even if no change detected\n                                        // (user might have just viewed the portal without making changes)\n                                        sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success('Billing settings updated successfully!');\n                                        console.log('No plan change detected, but showing success message');\n                                        break;\n                                    }\n                                }\n                                attempts++;\n                                if (attempts < maxAttempts) {\n                                    console.log(\"No change detected yet, waiting \".concat(retryDelay, \"ms before next attempt...\"));\n                                    await new Promise({\n                                        \"BillingPage.useEffect.refreshWithRetry\": (resolve)=>setTimeout(resolve, retryDelay)\n                                    }[\"BillingPage.useEffect.refreshWithRetry\"]);\n                                }\n                            } catch (error) {\n                                console.error(\"Refresh attempt \".concat(attempts + 1, \" failed:\"), error);\n                                attempts++;\n                                if (attempts >= maxAttempts) {\n                                    console.log('Max refresh attempts reached, forcing page reload...');\n                                    sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error('Refreshing page to update subscription status...');\n                                    // Force a page reload as last resort\n                                    setTimeout({\n                                        \"BillingPage.useEffect.refreshWithRetry\": ()=>{\n                                            window.location.reload();\n                                        }\n                                    }[\"BillingPage.useEffect.refreshWithRetry\"], 2000);\n                                } else {\n                                    await new Promise({\n                                        \"BillingPage.useEffect.refreshWithRetry\": (resolve)=>setTimeout(resolve, retryDelay)\n                                    }[\"BillingPage.useEffect.refreshWithRetry\"]);\n                                }\n                            }\n                        }\n                    }\n                }[\"BillingPage.useEffect.refreshWithRetry\"];\n                refreshWithRetry();\n            }\n        }\n    }[\"BillingPage.useEffect\"], [\n        searchParams,\n        refreshSubscription,\n        user === null || user === void 0 ? void 0 : user.id\n    ]); // Removed subscriptionStatus?.tier from dependencies\n    const handleChangePlan = async ()=>{\n        // For existing users (anyone who can access the billing page), always use Customer Portal\n        // The billing page is only accessible to authenticated users with accounts\n        try {\n            setLoading(true);\n            // Create return URL with current domain, portal return parameter, and current tier\n            const currentTier = (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) || 'free';\n            const returnUrl = \"\".concat(window.location.origin, \"/billing?portal_return=true&prev_tier=\").concat(currentTier);\n            await openCustomerPortal(returnUrl);\n        } catch (error) {\n            console.error('Customer portal error:', error);\n            // If portal is not configured, fall back to showing a helpful message\n            if (error.message.includes('No configuration provided') || error.message.includes('default configuration has not been created')) {\n                sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error('Billing portal is being set up. Please contact support for plan changes.');\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error('Failed to open billing portal. Please try again.');\n            }\n            setLoading(false);\n        }\n    };\n    const handleCancelSubscription = async ()=>{\n        if (!cancelReason.trim()) {\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error('Please select a reason for cancellation');\n            return;\n        }\n        setLoading(true);\n        try {\n            var _user_user_metadata;\n            // Send cancellation feedback via EmailJS\n            const templateParams = {\n                user_email: (user === null || user === void 0 ? void 0 : user.email) || 'Unknown',\n                user_name: (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.first_name) || 'User',\n                current_plan: currentPlan.name,\n                cancel_reason: cancelReason,\n                additional_feedback: cancelFeedback,\n                cancel_date: new Date().toLocaleDateString()\n            };\n            await _emailjs_browser__WEBPACK_IMPORTED_MODULE_10__[\"default\"].send(\"service_2xtn7iv\", \"template_pg7e1af\", templateParams, \"lm7-ATth2Cql60KIN\");\n            // Here you would also cancel the subscription in your payment processor\n            // For now, we'll simulate the process\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success('Subscription cancelled successfully. We\\'ve sent your feedback to our team.');\n            setShowCancelModal(false);\n            setCancelReason('');\n            setCancelFeedback('');\n            await refreshSubscription();\n        } catch (error) {\n            console.error('Cancellation error:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error('Failed to cancel subscription. Please contact support.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const cancelReasons = [\n        'Too expensive',\n        'Not using enough features',\n        'Found a better alternative',\n        'Technical issues',\n        'Poor customer support',\n        'Missing features I need',\n        'Temporary financial constraints',\n        'Other'\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-slide-in\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-2\",\n                                    children: \"Billing & Plans \\uD83D\\uDCB3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 text-lg\",\n                                    children: \"Manage your subscription, billing information, and plan features.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            onClick: refreshSubscription,\n                            className: \"text-gray-600 border-gray-300 hover:bg-gray-50\",\n                            size: \"sm\",\n                            children: \"\\uD83D\\uDD04 Refresh Status\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                    lineNumber: 333,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                lineNumber: 332,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card p-8 hover:shadow-lg transition-all duration-200 animate-slide-in\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 bg-orange-100 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-6 w-6 text-orange-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-900\",\n                                children: \"Current Plan\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-3xl font-bold text-gray-900\",\n                                                children: currentPlan.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 15\n                                            }, this),\n                                            currentPlan.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                className: \"bg-gradient-to-r from-orange-500 to-orange-600 text-white px-3 py-1 text-sm font-semibold\",\n                                                children: \"⭐ Popular\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl font-semibold text-gray-700\",\n                                                children: currentPlan.price === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600\",\n                                                    children: \"Free forever\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"$\",\n                                                        currentPlan.price,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500 font-normal\",\n                                                            children: [\n                                                                \"/\",\n                                                                currentPlan.interval\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 15\n                                            }, this),\n                                            daysUntilRenewal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 text-sm text-gray-500 bg-gray-50 px-3 py-2 rounded-lg inline-flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Renews in \",\n                                                            daysUntilRenewal,\n                                                            \" days\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) !== 'free' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowCancelModal(true),\n                                    className: \"text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300 transition-all duration-200\",\n                                    children: \"Cancel Subscription\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                lineNumber: 354,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-slide-in\",\n                style: {\n                    animationDelay: '200ms'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"Plan Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-px bg-gradient-to-r from-orange-200 to-transparent flex-1 ml-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-2xl mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card relative overflow-hidden transition-all duration-300 hover:shadow-xl\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"inline-flex items-center gap-2 bg-green-100 text-green-800 px-4 py-2 rounded-full text-sm font-semibold mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Current Plan\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                                children: currentPlan.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-5xl font-bold text-gray-900\",\n                                                        children: [\n                                                            \"$\",\n                                                            currentPlan.price\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    currentPlan.price > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500 text-xl\",\n                                                        children: [\n                                                            \"/\",\n                                                            currentPlan.interval\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 17\n                                            }, this),\n                                            currentPlan.price === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-green-600 font-semibold text-lg\",\n                                                children: \"Forever free\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                children: \"Your Current Features:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                                                children: currentPlan.features.slice(0, 6).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-shrink-0\",\n                                                                children: feature.included ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-1 bg-green-100 rounded-full\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                        lineNumber: 440,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                    lineNumber: 439,\n                                                                    columnNumber: 27\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-1 bg-gray-100 rounded-full\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                        lineNumber: 444,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                    lineNumber: 443,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm \".concat(feature.included ? 'text-gray-900' : 'text-gray-400'),\n                                                                children: [\n                                                                    feature.name,\n                                                                    feature.limit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500 font-medium\",\n                                                                        children: [\n                                                                            \" (\",\n                                                                            feature.limit,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                        lineNumber: 453,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: handleChangePlan,\n                                                disabled: loading,\n                                                className: \"w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg hover:shadow-xl transition-all duration-200\",\n                                                size: \"lg\",\n                                                children: (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Choose a Paid Plan\"\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 476,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Change Plan or Cancel\"\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-center text-sm text-gray-500\",\n                                                children: (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' ? 'Upgrade to unlock more features and higher limits' : 'Manage your subscription, change plans, or cancel anytime'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 410,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                lineNumber: 403,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 animate-slide-in\",\n                style: {\n                    animationDelay: '400ms'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-blue-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-6 w-6 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"Billing Information\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 498,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center py-3 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Current Plan\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-gray-900\",\n                                                children: currentPlan.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 505,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center py-3 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Billing Cycle\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-gray-900\",\n                                                children: currentPlan.price === 0 ? 'N/A' : \"Monthly (\".concat(currentPlan.interval, \")\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 13\n                                    }, this),\n                                    daysUntilRenewal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center py-3 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Next Billing Date\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 517,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-gray-900\",\n                                                children: new Date(Date.now() + daysUntilRenewal * 24 * 60 * 60 * 1000).toLocaleDateString()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center py-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                className: \"\".concat((subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' ? 'bg-gray-100 text-gray-800' : 'bg-green-100 text-green-800'),\n                                                children: (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' ? 'Free Plan' : 'Active'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 504,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 497,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-purple-100 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-6 w-6 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 539,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"Need Help?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 542,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 538,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Have questions about your subscription or need assistance with billing?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            className: \"w-full justify-start\",\n                                            onClick: ()=>window.open('/contact', '_blank'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Contact Support\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 544,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 537,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                lineNumber: 495,\n                columnNumber: 7\n            }, this),\n            showCancelModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-2xl max-w-md w-full p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-6 w-6 text-red-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 567,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Cancel Subscription\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 566,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"We're sorry to see you go! Please help us improve by telling us why you're cancelling.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 571,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Reason for cancellation *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: cancelReason,\n                                            onChange: (e)=>setCancelReason(e.target.value),\n                                            className: \"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Select a reason...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 585,\n                                                    columnNumber: 19\n                                                }, this),\n                                                cancelReasons.map((reason)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: reason,\n                                                        children: reason\n                                                    }, reason, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 576,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Additional feedback (optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: cancelFeedback,\n                                            onChange: (e)=>setCancelFeedback(e.target.value),\n                                            placeholder: \"Tell us more about your experience or what we could do better...\",\n                                            rows: 3,\n                                            className: \"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 592,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 575,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowCancelModal(false),\n                                    className: \"flex-1\",\n                                    children: \"Keep Subscription\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 607,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleCancelSubscription,\n                                    disabled: loading || !cancelReason.trim(),\n                                    className: \"flex-1 bg-red-600 hover:bg-red-700\",\n                                    children: loading ? 'Cancelling...' : 'Cancel Subscription'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 614,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 606,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                    lineNumber: 565,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                lineNumber: 564,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: confirmation.isOpen,\n                onClose: confirmation.hideConfirmation,\n                onConfirm: confirmation.onConfirm,\n                title: confirmation.title,\n                message: confirmation.message,\n                confirmText: confirmation.confirmText,\n                cancelText: confirmation.cancelText,\n                type: confirmation.type,\n                isLoading: confirmation.isLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                lineNumber: 627,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n        lineNumber: 330,\n        columnNumber: 5\n    }, this);\n}\n_s(BillingPage, \"ao9wCuBkrny2/ktyGsoTqY438I0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_6__.useSubscription,\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_9__.useConfirmation\n    ];\n});\n_c = BillingPage;\nvar _c;\n$RefreshReg$(_c, \"BillingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/billing/page.tsx\n"));

/***/ })

});